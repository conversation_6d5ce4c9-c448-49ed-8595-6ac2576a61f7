#!/usr/bin/env python3
"""
批量标记巡检对象脚本
根据 inspection_object.json 中的数据，在对应图片上标记检测到的对象
"""

import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import glob

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入标记函数
from test.analyze.mark_inspection_obj import mark_inspection_object


def load_inspection_data(json_path: str) -> List[Dict[str, Any]]:
    """
    加载巡检对象数据
    
    Args:
        json_path: JSON文件路径
        
    Returns:
        巡检对象数据列表
    """
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ 成功加载 {len(data)} 条巡检数据")
        return data
    except Exception as e:
        print(f"❌ 加载JSON文件失败: {e}")
        return []


def find_image_file(name: str, image_dir: str) -> str:
    """
    根据名称前缀查找对应的图片文件
    
    Args:
        name: 图片名称前缀（如 "frame_000774_t25.80s"）
        image_dir: 图片目录路径
        
    Returns:
        完整的图片文件路径，如果未找到返回空字符串
    """
    # 构建搜索模式：name + 任意字符 + .jpg
    pattern = os.path.join(image_dir, f"{name}*.jpg")
    matches = glob.glob(pattern)
    
    if matches:
        return matches[0]  # 返回第一个匹配的文件
    else:
        return ""


def process_inspection_objects(
    json_path: str = "test/analyze/inspection_object.json",
    image_dir: str = "assets/extracted_frames_20250904_170940",
    output_dir: str = "test/analyze/marked_inspection_objects"
) -> None:
    """
    批量处理巡检对象标记
    
    Args:
        json_path: JSON数据文件路径
        image_dir: 图片目录路径
        output_dir: 输出目录路径
    """
    print("🚀 开始批量标记巡检对象...")
    
    # 加载数据
    inspection_data = load_inspection_data(json_path)
    if not inspection_data:
        print("❌ 没有可处理的数据")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 统计信息
    total_objects = len(inspection_data)
    processed_count = 0
    success_count = 0
    failed_count = 0
    
    print(f"📊 总共需要处理 {total_objects} 个对象")
    print("-" * 60)
    
    for i, obj in enumerate(inspection_data, 1):
        name = obj.get("name", "")
        targets = obj.get("target", [])
        bboxes = obj.get("grounding_bbox", [])
        description = obj.get("description", "")
        confidence = obj.get("confidence", 0)
        
        print(f"\n[{i}/{total_objects}] 处理对象: {name}")
        print(f"📝 描述: {description}")
        print(f"🎯 目标数量: {len(targets)}")
        print(f"📍 边界框数量: {len(bboxes)}")
        print(f"🔍 置信度: {confidence}")
        
        # 查找对应的图片文件
        image_path = find_image_file(name, image_dir)
        if not image_path:
            print(f"❌ 未找到对应的图片文件: {name}")
            failed_count += 1
            continue
            
        print(f"📷 找到图片: {os.path.basename(image_path)}")
        
        # 检查目标和边界框数量是否匹配
        if len(targets) != len(bboxes):
            print(f"⚠️ 目标数量({len(targets)})与边界框数量({len(bboxes)})不匹配")
            # 取较小的数量进行处理
            min_count = min(len(targets), len(bboxes))
            targets = targets[:min_count]
            bboxes = bboxes[:min_count]
            print(f"🔧 调整为处理 {min_count} 个目标")
        
        # 处理每个目标和对应的边界框
        object_success = True
        for j, (target, bbox) in enumerate(zip(targets, bboxes)):
            print(f"  🏷️ 标记目标 {j+1}: {target}")
            print(f"  📦 边界框: {bbox}")
            
            try:
                # 调用标记函数
                success = mark_inspection_object(
                    image_path=image_path,
                    bbox=tuple(bbox),  # 确保是元组格式
                    output_dir=output_dir,
                    label=target,
                    show_image=False
                )
                
                if success:
                    print(f"  ✅ 成功标记目标: {target}")
                else:
                    print(f"  ❌ 标记失败: {target}")
                    object_success = False
                    
            except Exception as e:
                print(f"  ❌ 标记过程出错: {e}")
                object_success = False
        
        processed_count += 1
        if object_success:
            success_count += 1
        else:
            failed_count += 1
            
        print(f"📈 进度: {processed_count}/{total_objects} ({processed_count/total_objects*100:.1f}%)")
    
    # 输出最终统计
    print("\n" + "="*60)
    print("📊 处理完成统计:")
    print(f"  总对象数: {total_objects}")
    print(f"  已处理: {processed_count}")
    print(f"  成功: {success_count}")
    print(f"  失败: {failed_count}")
    print(f"  成功率: {success_count/processed_count*100:.1f}%" if processed_count > 0 else "成功率: 0%")
    print(f"📁 标记后的图片保存在: {output_dir}")
    print("🎉 批量标记完成!")


def main():
    """主函数"""
    # 默认路径配置
    json_path = "test/analyze/inspection_object.json"
    image_dir = "assets/extracted_frames_20250904_170940"
    output_dir = "test/analyze/marked_inspection_objects"
    
    # 检查文件和目录是否存在
    if not os.path.exists(json_path):
        print(f"❌ JSON文件不存在: {json_path}")
        return
        
    if not os.path.exists(image_dir):
        print(f"❌ 图片目录不存在: {image_dir}")
        return
    
    # 开始处理
    process_inspection_objects(json_path, image_dir, output_dir)


if __name__ == "__main__":
    main()
